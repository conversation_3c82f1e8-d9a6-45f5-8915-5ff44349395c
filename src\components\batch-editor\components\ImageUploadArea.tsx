'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/Dialog';
import { Input } from '@/components/ui/Input';
import { VisuallyHidden } from '@/components/ui/VisuallyHidden';
import type { DropzoneRootProps } from 'react-dropzone';
import Image from 'next/image';
import type { ImageState } from '@/lib/store/imageStore';
import { useImageStore } from '@/lib/store/imageStore';
import UploadIcon from '@/components/icons/Upload';
import { EditableImageName } from './EditableImageName';
import { formatFileSize } from '@/lib/utils/imageCompress';
import DeleteIcon from '@/components/icons/Delete';

interface ImageUploadGridProps {
  getRootProps: () => DropzoneRootProps;
  open: () => void;
  isDragActive: boolean;
  handleLoadFromUrl: (url: string) => Promise<void>;
  handleLoadSampleImage: (
    url: string,
    name: string,
    processedUrl?: string
  ) => Promise<void>;
  imagesCount: number;
  images?: ImageState[];
  processingImageIds?: Set<string>;
}

export function ImageUploadGrid({
  getRootProps,
  open,
  isDragActive,
  handleLoadFromUrl,
  imagesCount,
  images = [],
  processingImageIds = new Set(),
}: ImageUploadGridProps) {
  const [isUrlDialogOpen, setIsUrlDialogOpen] = useState(false);
  const [inputUrl, setInputUrl] = useState('');
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);

  const handleUrlSubmit = async () => {
    if (!inputUrl.trim()) return;

    setIsLoadingUrl(true);
    try {
      await handleLoadFromUrl(inputUrl);
      setIsUrlDialogOpen(false);
      setInputUrl('');
    } finally {
      setIsLoadingUrl(false);
    }
  };

  // 渲染上传卡片
  const renderUploadCard = () => (
    <div
      {...getRootProps()}
      className={`w-[222px] h-[222px] border border-[#E7E7E7] rounded-2xl flex flex-col items-center justify-center cursor-pointer transition-colors interactive-container hover:border-2 hover:text-brand-primary hover:border-primary`}
      style={{
        boxShadow:
          '0px 7px 14px 0px rgba(220, 223, 228, 0.16), 0px 8px 16px 0px rgba(220, 223, 228, 0.12), 0px 10px 32px 0px rgba(220, 223, 228, 0.08)',
      }}
      onClick={e => {
        e.stopPropagation();
        // 检查图片数量限制
        if (imagesCount >= 10) {
          // 这里应该显示提示，但为了简化，我们先注释掉
          // showTips('error', 'The number of images exceeds the limit, and a maximum of 10 images can be uploaded.', 4000);
          return;
        }
        open();
      }}
    >
      <div className='mb-2 flex items-center justify-center'>
        {/* 导出图标 */}
        <UploadIcon className='icon-interactive size-12' />
      </div>
      <span className='text-base font-normal'>Upload Image</span>
    </div>
  );

  // 渲染图片卡片
  const renderImageCard = (image: ImageState) => (
    <div key={image.id} className={`w-[222px] transition-all`}>
      {/* 图片容器 */}
      <div
        className='w-[222px] h-[222px] border border-[#E7E7E7] rounded-2xl overflow-hidden relative'
        style={{
          // 如果有合成预览URL，使用白色背景；否则使用设置的背景
          backgroundColor: image.compositePreviewUrl
            ? '#ffffff'
            : image.backgroundImageUrl
              ? 'transparent'
              : image.backgroundColor || '#ffffff',
          backgroundImage: image.compositePreviewUrl
            ? undefined
            : image.backgroundImageUrl
              ? `url(${image.backgroundImageUrl})`
              : undefined,
          backgroundSize: image.compositePreviewUrl
            ? undefined
            : image.backgroundImageUrl
              ? 'cover'
              : undefined,
          backgroundPosition: image.compositePreviewUrl
            ? undefined
            : image.backgroundImageUrl
              ? 'center'
              : undefined,
          backgroundRepeat: image.compositePreviewUrl
            ? undefined
            : image.backgroundImageUrl
              ? 'no-repeat'
              : undefined,
          boxShadow:
            '0px 6.216px 12.432px 0px rgba(220, 223, 228, 0.16), 0px 7.104px 14.208px 0px rgba(220, 223, 228, 0.12), 0px 8.88px 28.416px 0px rgba(220, 223, 228, 0.08)',
        }}
      >
        <Image
          src={
            image.compositePreviewUrl ||
            image.resizedUrl ||
            image.processedUrl ||
            image.previewUrl
          }
          alt={image.name}
          fill
          className={`object-cover ${
            image.compositePreviewUrl || image.resizedUrl || image.processedUrl
              ? 'object-contain'
              : 'object-cover'
          }`}
          draggable={false}
        />

        {/* 统一处理中状态 */}
        {processingImageIds.has(image.id) && (
          <div className='absolute inset-0 flex items-center justify-center bg-[rgba(18,18,18,0.10)] backdrop-blur-[4px]'>
            <div className='w-12 h-12 flex items-center justify-center'>
              {/* 锁图标 */}
              <Image
                src='/apps/icons/loading.png'
                alt='loading'
                width={48}
                height={48}
                className='animate-spin'
              />
            </div>
          </div>
        )}

        {/* 锁定状态 - 非会员限制 */}
        {image.status === 'locked' && !processingImageIds.has(image.id) && (
          <div className='absolute inset-0 flex items-center justify-center bg-[rgba(18,18,18,0.10)] backdrop-blur-[4px]'>
            <div className='w-12 h-12 flex items-center justify-center'>
              {/* 锁图标 */}
              <Image
                src='/apps/icons/lock.svg'
                alt='lock'
                width={48}
                height={48}
              />
            </div>
          </div>
        )}

        {/* 如果有处理后的图片且不在处理中且不是锁定状态，显示成功图标 */}
      </div>

      {/* 图片信息 */}
      <div className='mt-2 text-left px-2'>
        <div className='w-[206px]'>
          <EditableImageName
            name={image.name}
            onNameChange={newName => {
              useImageStore.getState().updateImageName(image.id, newName);
            }}
            className='w-full'
          />
        </div>
        <div className='text-[#878787] text-sm space-y-1'>
          {/* 显示尺寸变化或原始尺寸 */}
          <div>
            {image.targetWidth && image.targetHeight ? (
              <div className='flex items-center justify-start gap-1'>
                <span>
                  {image.originalWidth || image.width}x
                  {image.originalHeight || image.height}
                </span>
                <span>→</span>
                <span
                  className='px-2 py-1 rounded text-sm font-medium'
                  style={{ backgroundColor: '#D7EFCE', color: '#2D5A3D' }}
                >
                  {image.targetWidth}x{image.targetHeight}
                </span>
              </div>
            ) : (
              <span>
                {image.width}x{image.height}
              </span>
            )}
          </div>

          {/* 显示文件大小变化 */}
          {(() => {
            // 优先显示压缩后的大小变化（压缩是最终操作）
            if (image.compressedSize && image.originalSize) {
              return (
                <div className='flex items-center justify-start gap-1'>
                  <span>{formatFileSize(image.originalSize)}</span>
                  <span>→</span>
                  <span
                    className='px-2 py-1 rounded text-sm font-medium'
                    style={{ backgroundColor: '#D7EFCE', color: '#2D5A3D' }}
                  >
                    {formatFileSize(image.compressedSize)}
                  </span>
                </div>
              );
            }

            // 如果有任何操作导致文件大小发生变化，显示大小变化
            // 这包括尺寸调整、格式转换等所有操作的累积效果
            if (
              image.originalSize &&
              image.size !== image.originalSize &&
              (image.targetWidth || image.targetHeight || image.convertedFormat)
            ) {
              return (
                <div className='flex items-center justify-start gap-1'>
                  <span>{formatFileSize(image.originalSize)}</span>
                  <span>→</span>
                  <span
                    className='px-2 py-1 rounded text-sm font-medium'
                    style={{ backgroundColor: '#D7EFCE', color: '#2D5A3D' }}
                  >
                    {formatFileSize(image.size)}
                  </span>
                </div>
              );
            }

            // 默认显示当前文件大小
            return image.size > 0 && <span>{formatFileSize(image.size)}</span>;
          })()}
        </div>
      </div>
    </div>
  );

  return (
    <div className='flex-1 bg-bg-light flex flex-col px-8 py-6'>
      {/* 主要上传区域 */}
      {images.length > 0 ? (
        <div>
          {/* 底部导航栏 */}
          <div className='flex justify-between items-center pb-6'>
            {/* 左侧导航按钮 */}
            <div className='flex items-center gap-2'>
              <Button
                variant='ghost'
                title='Previous step'
                size='icon'
                className='w-8 h-8 p-0 m-0'
              >
                <Image
                  src='/apps/icons/previous.svg'
                  alt='previous'
                  width={24}
                  height={24}
                />
              </Button>
              <Button
                variant='ghost'
                title='Next setp'
                size='icon'
                className='w-8 h-8 p-0 m-0'
              >
                <Image
                  src='/apps/icons/next.svg'
                  alt='next'
                  width={24}
                  height={24}
                />
              </Button>
            </div>

            {/* 右侧计数和删除按钮 */}
            <div className='flex items-center gap-4'>
              <div className='bg-[rgba(255,204,3,0.1)] rounded-full px-4 py-1'>
                <span className='text-[#FF781F] text-base font-medium'>
                  {images.length} / 50
                </span>
              </div>
              {/* 删除按钮 */}
              <Button
                variant='ghost'
                size='icon'
                className='interactive-container cursor-pointer w-8 h-8'
                onClick={e => {
                  e.stopPropagation();
                }}
              >
                <DeleteIcon className='icon-interactive w-8 h-8' />
              </Button>
            </div>
          </div>
          {/* 主要内容区域 */}
          <div className='flex-1'>
            {/* 网格容器 */}
            <div className='grid gap-3 grid-cols-[repeat(auto-fill,222px)] auto-rows-max'>
              {/* 第一个位置始终是上传卡片 */}
              {renderUploadCard()}

              {/* 显示已上传的图片 */}
              {images.map(renderImageCard)}
            </div>
          </div>
        </div>
      ) : (
        <div className='bg-white w-full h-full rounded-3xl shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)] p-4'>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-2xl h-full flex flex-col items-center justify-center relative ${
              isDragActive ? 'border-blue-500 bg-blue-50' : 'border-[#d0d0d0]'
            }`}
          >
            {/* 装饰性图片图标 */}
            <div className='relative mb-4'>
              {/* 主图片图标容器 */}
              <div className='relative w-[200px] h-[200px]'>
                {/* 背景装饰圆 */}
                <div className='absolute inset-0 bg-gradient-to-br from-white/10 to-white/0 rounded-full blur-xl'></div>

                {/* 主要图片卡片 */}
                <div className='absolute top-[44px] left-[29px] w-[135px] h-[112px] bg-gradient-to-b from-[#FFCC03] to-[#FFEDA5] rounded-2xl border border-white/20 shadow-[0px_4px_16px_0px_rgba(119,109,47,0.15)]'>
                  {/* 图片占位符 */}
                  <div className='absolute top-[53px] left-[9px] w-[74px] h-[48px] bg-gradient-to-b from-white to-[#FFEDA9] rounded-lg border border-white/20 shadow-[0px_2px_25px_0px_rgba(173,132,44,0.3)]'></div>
                  <div className='absolute top-[36px] left-[33px] w-[100px] h-[65px] bg-gradient-to-b from-white to-[#FFEDA9] rounded-lg border border-white/20 shadow-[0px_2px_25px_0px_rgba(173,132,44,0.3)]'></div>

                  {/* 小装饰圆 */}
                  <div className='absolute top-[18px] left-[20px] w-[20px] h-[20px] bg-gradient-to-b from-white to-white/40 rounded-full border border-white/20'></div>
                </div>

                {/* 右下角装饰圆 */}
                <div className='absolute bottom-[19px] right-[7px] w-[62px] h-[62px] bg-gradient-to-b from-white to-white/10 rounded-full border border-white/20 shadow-[0px_4px_16px_0px_rgba(35,24,13,0.15)]'>
                  {/* 加号图标 */}
                  <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'>
                    <div className='w-[28px] h-[28px] relative'>
                      <div className='absolute top-[12px] left-0 w-[28px] h-[4px] bg-[#FECB04] rounded-sm'></div>
                      <div className='absolute top-0 left-[12px] w-[4px] h-[28px] bg-[#FECB04] rounded-sm'></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className='text-center'>
              <p className='text-text-primary text-2xl font-bold mb-2 leading-tight'>
                Drag and drop your image here
              </p>
              <p className='text-text-secondary text-base mb-4 leading-relaxed'>
                Paste image (⌘ V) or{' '}
                <Dialog
                  open={isUrlDialogOpen}
                  onOpenChange={setIsUrlDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant='link'
                      className='p-0 h-auto text-base text-[#ffcc03] hover:text-[#ffdb4d] underline'
                      onClick={e => e.stopPropagation()}
                    >
                      URL
                    </Button>
                  </DialogTrigger>
                  <DialogContent className='w-[542px] bg-white rounded-2xl shadow-[0px_4px_8px_0px_rgba(19,19,20,0.5)] border-0 p-0'>
                    <VisuallyHidden>
                      <DialogTitle>输入图片链接</DialogTitle>
                    </VisuallyHidden>
                    {/* 顶部关闭按钮区域 */}
                    <div className='h-10 relative rounded-t-[6px] w-full border-b border-[#e7e7e7]'></div>

                    {/* 主要内容区域 */}
                    <div className='px-8 py-6'>
                      <div className='flex flex-col gap-6'>
                        {/* 标题和输入框 */}
                        <div className='flex flex-col gap-3'>
                          <h3 className='text-[#121212] text-[16px] font-medium  leading-[1.5]'>
                            Paste Image URL
                          </h3>
                          <div className='relative'>
                            <Input
                              placeholder='Please input image URL'
                              value={inputUrl}
                              onChange={e => setInputUrl(e.target.value)}
                              onKeyDown={e => {
                                if (e.key === 'Enter') {
                                  handleUrlSubmit();
                                }
                              }}
                              disabled={isLoadingUrl}
                              className='h-10 bg-[#f9fafb] border-[#e7e7e7] rounded-lg px-3 py-[9px] text-[14px]  placeholder:text-[#b8b8b8] focus:ring-2 focus:ring-[#ffcc03] focus:border-[#ffcc03]'
                            />
                          </div>
                        </div>

                        {/* 按钮区域 */}
                        <div className='flex justify-end gap-3'>
                          <Button
                            variant='outline'
                            onClick={() => {
                              setIsUrlDialogOpen(false);
                              setInputUrl('');
                            }}
                            disabled={isLoadingUrl}
                            className='h-10 w-24 rounded-lg border-[#e7e7e7] bg-white text-[#000000] text-[16px] font-medium  hover:bg-gray-50'
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={handleUrlSubmit}
                            disabled={!inputUrl.trim() || isLoadingUrl}
                            className='h-10 w-24 rounded-lg bg-[#ffcc03] text-[#000000] text-[16px] font-medium  hover:bg-[#ffcc03]/90 border-0'
                          >
                            {isLoadingUrl ? (
                              <Image
                                src='/apps/icons/loading.png'
                                alt='loading'
                                width={16}
                                height={16}
                                className='animate-spin'
                              />
                            ) : (
                              'Confirm'
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </p>

              <Button
                className='h-12 px-6 w-66 cursor-pointer'
                onClick={e => {
                  e.stopPropagation();
                  // 检查图片数量限制
                  if (imagesCount >= 10) {
                    // 这里应该显示提示，但为了简化，我们先注释掉
                    // showTips('error', 'The number of images exceeds the limit, and a maximum of 10 images can be uploaded.', 4000);
                    return;
                  }
                  open();
                }}
              >
                <Image
                  src='/apps/icons/add.svg'
                  alt='add'
                  width={24}
                  height={24}
                />
                <span>Select image</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
