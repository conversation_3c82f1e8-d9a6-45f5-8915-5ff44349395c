'use client';

import { useCallback, useRef } from 'react';
import { useImageStore } from '../store/imageStore';
import { 
  useUnifiedHistoryStore, 
  historyActions, 
  type OperationType, 
  type ImageSnapshot,
  type HistoryItem 
} from '../store/unifiedHistoryStore';

/**
 * 统一历史记录管理 Hook
 * 为单个和批量编辑器提供统一的历史记录功能
 */
export const useUnifiedHistory = () => {
  // 获取历史记录状态
  const { canUndo, canRedo, history, currentIndex, isEnabled } = useUnifiedHistoryStore();
  
  // 获取图片 store
  const imageStore = useImageStore.getState();
  const images = useImageStore(state => state.images);
  
  // UI 更新回调
  const uiUpdateCallbacks = useRef<Set<() => void>>(new Set());

  // ==================== 核心功能 ====================

  /**
   * 记录操作前后的状态变化
   */
  const recordOperation = useCallback((
    operation: OperationType,
    description: string,
    affectedImageIds: string[],
    beforeStates: Record<string, ImageSnapshot>,
    afterStates: Record<string, ImageSnapshot>,
    metadata?: Record<string, any>
  ) => {
    if (!isEnabled) {
      console.log('历史记录已禁用，跳过记录');
      return;
    }

    historyActions.record({
      operation,
      description,
      affectedImageIds,
      beforeStates,
      afterStates,
      metadata,
    });

    console.log('✅ 已记录操作:', {
      operation,
      description,
      affectedImages: affectedImageIds.length,
    });
  }, [isEnabled]);

  /**
   * 创建当前图片状态快照
   */
  const createCurrentSnapshot = useCallback((imageIds: string[]): Record<string, ImageSnapshot> => {
    const snapshots: Record<string, ImageSnapshot> = {};
    
    for (const imageId of imageIds) {
      const imageData = images.get(imageId);
      if (imageData) {
        snapshots[imageId] = useUnifiedHistoryStore.getState().createImageSnapshot(imageId, imageData);
      }
    }
    
    return snapshots;
  }, [images]);

  /**
   * 恢复图片状态
   */
  const restoreImageStates = useCallback((states: Record<string, ImageSnapshot>) => {
    console.log('🔄 开始恢复图片状态:', Object.keys(states));

    // 暂停历史记录，避免恢复过程产生新的历史记录
    historyActions.setEnabled(false);

    try {
      for (const [imageId, snapshot] of Object.entries(states)) {
        const currentImage = images.get(imageId);
        if (currentImage) {
          // 恢复图片状态
          imageStore.updateImage(imageId, {
            name: snapshot.name,
            width: snapshot.width,
            height: snapshot.height,
            size: snapshot.size,
            status: snapshot.status,
            
            // 背景相关
            backgroundColor: snapshot.backgroundColor || 'transparent',
            backgroundImageUrl: snapshot.backgroundImageUrl,
            backgroundImageId: snapshot.backgroundImageId,
            processedUrl: snapshot.processedUrl,
            
            // 尺寸相关
            targetWidth: snapshot.targetWidth,
            targetHeight: snapshot.targetHeight,
            originalWidth: snapshot.originalWidth,
            originalHeight: snapshot.originalHeight,
            resizedUrl: snapshot.resizedUrl,
            
            // 压缩相关
            originalSize: snapshot.originalSize,
            compressedSize: snapshot.compressedSize,
            compressedUrl: snapshot.compressedUrl,
            compressionLevel: snapshot.compressionLevel,
            
            // 格式转换相关
            originalFormat: snapshot.originalFormat,
            convertedFormat: snapshot.convertedFormat,
            convertedUrl: snapshot.convertedUrl,
            
            // 预览相关
            compositePreviewUrl: snapshot.compositePreviewUrl,
          });
        }
      }

      // 批量更新预览URL
      const imageIds = Object.keys(states);
      imageStore.batchUpdatePreviewUrls(imageIds).catch(error => {
        console.error('批量更新预览URL失败:', error);
      });

      console.log('✅ 图片状态恢复完成');
    } finally {
      // 恢复历史记录功能
      historyActions.setEnabled(true);
    }
  }, [images, imageStore]);

  /**
   * 撤销操作
   */
  const handleUndo = useCallback(() => {
    if (!canUndo) {
      console.warn('无法撤销：没有可撤销的操作');
      return false;
    }

    const targetItem = historyActions.undo();
    if (targetItem) {
      console.log('🔙 撤销到:', targetItem.description);
      
      // 恢复到目标状态（使用 beforeStates）
      restoreImageStates(targetItem.beforeStates);
      
      // 通知UI更新
      notifyUIUpdate();
      
      return true;
    }

    return false;
  }, [canUndo, restoreImageStates]);

  /**
   * 重做操作
   */
  const handleRedo = useCallback(() => {
    if (!canRedo) {
      console.warn('无法重做：没有可重做的操作');
      return false;
    }

    const targetItem = historyActions.redo();
    if (targetItem) {
      console.log('🔜 重做到:', targetItem.description);
      
      // 恢复到目标状态（使用 afterStates）
      restoreImageStates(targetItem.afterStates);
      
      // 通知UI更新
      notifyUIUpdate();
      
      return true;
    }

    return false;
  }, [canRedo, restoreImageStates]);

  // ==================== 便捷操作方法 ====================

  /**
   * 记录单个图片操作
   */
  const recordSingleImageOperation = useCallback((
    imageId: string,
    operation: OperationType,
    description: string,
    metadata?: Record<string, any>
  ) => {
    const beforeState = createCurrentSnapshot([imageId]);
    
    // 返回一个函数，在操作完成后调用以记录 afterState
    return () => {
      const afterState = createCurrentSnapshot([imageId]);
      recordOperation(operation, description, [imageId], beforeState, afterState, metadata);
    };
  }, [createCurrentSnapshot, recordOperation]);

  /**
   * 记录批量操作
   */
  const recordBatchOperation = useCallback((
    imageIds: string[],
    operation: OperationType,
    description: string,
    metadata?: Record<string, any>
  ) => {
    const beforeStates = createCurrentSnapshot(imageIds);
    
    // 返回一个函数，在操作完成后调用以记录 afterStates
    return () => {
      const afterStates = createCurrentSnapshot(imageIds);
      recordOperation(operation, description, imageIds, beforeStates, afterStates, metadata);
    };
  }, [createCurrentSnapshot, recordOperation]);

  /**
   * 记录初始状态（用于批量编辑器）
   */
  const recordInitialState = useCallback((imageIds: string[]) => {
    const historyLength = useUnifiedHistoryStore.getState().getHistoryLength();
    
    // 只在没有历史记录时记录初始状态
    if (historyLength === 0 && imageIds.length > 0) {
      console.log('📝 记录初始状态:', imageIds.length, '张图片');
      
      const initialStates = createCurrentSnapshot(imageIds);
      
      recordOperation(
        'upload',
        '初始状态',
        imageIds,
        {}, // 初始状态没有 beforeStates
        initialStates
      );
    }
  }, [createCurrentSnapshot, recordOperation]);

  // ==================== UI 更新管理 ====================

  /**
   * 注册UI更新回调
   */
  const registerUIUpdateCallback = useCallback((callback: () => void) => {
    uiUpdateCallbacks.current.add(callback);
    
    // 返回取消注册的函数
    return () => {
      uiUpdateCallbacks.current.delete(callback);
    };
  }, []);

  /**
   * 通知UI更新
   */
  const notifyUIUpdate = useCallback(() => {
    uiUpdateCallbacks.current.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('UI更新回调执行失败:', error);
      }
    });
  }, []);

  // ==================== 管理功能 ====================

  /**
   * 清空历史记录
   */
  const clearHistory = useCallback(() => {
    historyActions.clear();
    notifyUIUpdate();
  }, [notifyUIUpdate]);

  /**
   * 设置历史记录启用状态
   */
  const setEnabled = useCallback((enabled: boolean) => {
    historyActions.setEnabled(enabled);
  }, []);

  /**
   * 获取历史记录信息
   */
  const getHistoryInfo = useCallback(() => {
    return {
      canUndo,
      canRedo,
      currentIndex,
      historyLength: history.length,
      isEnabled,
      currentOperation: currentIndex >= 0 ? history[currentIndex]?.operation : null,
    };
  }, [canUndo, canRedo, currentIndex, history, isEnabled]);

  // ==================== 返回接口 ====================

  return {
    // 状态
    canUndo,
    canRedo,
    isEnabled,
    
    // 核心操作
    handleUndo,
    handleRedo,
    clearHistory,
    
    // 记录操作
    recordOperation,
    recordSingleImageOperation,
    recordBatchOperation,
    recordInitialState,
    
    // 工具方法
    createCurrentSnapshot,
    restoreImageStates,
    
    // UI管理
    registerUIUpdateCallback,
    notifyUIUpdate,
    
    // 配置
    setEnabled,
    getHistoryInfo,
  };
};
