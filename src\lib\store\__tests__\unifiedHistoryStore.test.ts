import { describe, it, expect, beforeEach } from 'vitest';
import { useUnifiedHistoryStore, historyActions } from '../unifiedHistoryStore';
import type { ImageState } from '../imageStore';

// Mock ImageState for testing
const createMockImageState = (id: string, overrides: Partial<ImageState> = {}): ImageState => ({
  id,
  name: `image-${id}.png`,
  width: 800,
  height: 600,
  size: 1024000,
  status: 'original',
  history: [],
  timestamp: Date.now(),
  previewUrl: `blob:${id}`,
  backgroundColor: 'transparent',
  backgroundImageUrl: undefined,
  backgroundImageId: undefined,
  isBlurEnabled: false,
  blurAmount: 0,
  isEraseMode: false,
  isRestoreMode: false,
  eraseBrushSize: 20,
  eraseHistory: [],
  eraseOperationCount: 0,
  resizeMode: 'fit',
  originalFormat: 'png',
  ...overrides,
});

describe('UnifiedHistoryStore', () => {
  beforeEach(() => {
    // 清空历史记录
    historyActions.clear();
    historyActions.setEnabled(true);
  });

  it('should initialize with empty history', () => {
    const state = useUnifiedHistoryStore.getState();
    expect(state.history).toEqual([]);
    expect(state.currentIndex).toBe(-1);
    expect(state.canUndo()).toBe(false);
    expect(state.canRedo()).toBe(false);
  });

  it('should record a new operation', () => {
    const mockImage = createMockImageState('test-1');
    const beforeStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', mockImage)
    };
    
    const updatedImage = { ...mockImage, backgroundColor: 'red' };
    const afterStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', updatedImage)
    };

    historyActions.record({
      operation: 'batch_background',
      description: '批量应用背景颜色: red',
      affectedImageIds: ['test-1'],
      beforeStates,
      afterStates,
    });

    const state = useUnifiedHistoryStore.getState();
    expect(state.history).toHaveLength(1);
    expect(state.history[0].operation).toBe('batch_background');
    expect(state.history[0].description).toBe('批量应用背景颜色: red');
    expect(state.canUndo()).toBe(true);
    expect(state.canRedo()).toBe(false);
  });

  it('should prevent duplicate operations', () => {
    const mockImage = createMockImageState('test-1');
    const beforeStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', mockImage)
    };
    
    const updatedImage = { ...mockImage, backgroundColor: 'red' };
    const afterStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', updatedImage)
    };

    const operationData = {
      operation: 'batch_background' as const,
      description: '批量应用背景颜色: red',
      affectedImageIds: ['test-1'],
      beforeStates,
      afterStates,
    };

    // 记录第一次操作
    historyActions.record(operationData);
    expect(useUnifiedHistoryStore.getState().history).toHaveLength(1);

    // 记录相同的操作（应该被跳过）
    historyActions.record(operationData);
    expect(useUnifiedHistoryStore.getState().history).toHaveLength(1);
  });

  it('should allow operations with actual state changes', () => {
    const mockImage = createMockImageState('test-1');
    const beforeStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', mockImage)
    };
    
    // 第一次操作：改变背景颜色为红色
    const updatedImage1 = { ...mockImage, backgroundColor: 'red' };
    const afterStates1 = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', updatedImage1)
    };

    historyActions.record({
      operation: 'batch_background',
      description: '批量应用背景颜色: red',
      affectedImageIds: ['test-1'],
      beforeStates,
      afterStates: afterStates1,
    });

    // 第二次操作：改变背景颜色为蓝色（不同的状态变化）
    const updatedImage2 = { ...mockImage, backgroundColor: 'blue' };
    const afterStates2 = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', updatedImage2)
    };

    historyActions.record({
      operation: 'batch_background',
      description: '批量应用背景颜色: blue',
      affectedImageIds: ['test-1'],
      beforeStates: afterStates1, // 使用前一次的结果作为before状态
      afterStates: afterStates2,
    });

    expect(useUnifiedHistoryStore.getState().history).toHaveLength(2);
  });

  it('should handle undo and redo correctly', () => {
    const mockImage = createMockImageState('test-1');
    const beforeStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', mockImage)
    };
    
    const updatedImage = { ...mockImage, backgroundColor: 'red' };
    const afterStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', updatedImage)
    };

    historyActions.record({
      operation: 'batch_background',
      description: '批量应用背景颜色: red',
      affectedImageIds: ['test-1'],
      beforeStates,
      afterStates,
    });

    const state = useUnifiedHistoryStore.getState();
    expect(state.canUndo()).toBe(true);
    expect(state.canRedo()).toBe(false);

    // 执行撤销
    const undoResult = historyActions.undo();
    expect(undoResult).toBeTruthy();
    expect(undoResult?.description).toBe('批量应用背景颜色: red');

    const stateAfterUndo = useUnifiedHistoryStore.getState();
    expect(stateAfterUndo.canUndo()).toBe(false);
    expect(stateAfterUndo.canRedo()).toBe(true);

    // 执行重做
    const redoResult = historyActions.redo();
    expect(redoResult).toBeTruthy();
    expect(redoResult?.description).toBe('批量应用背景颜色: red');

    const stateAfterRedo = useUnifiedHistoryStore.getState();
    expect(stateAfterRedo.canUndo()).toBe(true);
    expect(stateAfterRedo.canRedo()).toBe(false);
  });

  it('should limit history size', () => {
    // 设置较小的历史记录限制
    historyActions.setMaxHistorySize(3);

    const mockImage = createMockImageState('test-1');
    
    // 添加4个操作（超过限制）
    for (let i = 0; i < 4; i++) {
      const beforeStates = {
        'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', mockImage)
      };
      
      const updatedImage = { ...mockImage, backgroundColor: `color-${i}` };
      const afterStates = {
        'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', updatedImage)
      };

      historyActions.record({
        operation: 'batch_background',
        description: `操作 ${i}`,
        affectedImageIds: ['test-1'],
        beforeStates,
        afterStates,
      });
    }

    const state = useUnifiedHistoryStore.getState();
    expect(state.history).toHaveLength(3); // 应该只保留最新的3个
    expect(state.history[0].description).toBe('操作 1'); // 最旧的应该是操作1（操作0被删除）
    expect(state.history[2].description).toBe('操作 3'); // 最新的应该是操作3
  });

  it('should clear history correctly', () => {
    const mockImage = createMockImageState('test-1');
    const beforeStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', mockImage)
    };
    
    const updatedImage = { ...mockImage, backgroundColor: 'red' };
    const afterStates = {
      'test-1': useUnifiedHistoryStore.getState().createImageSnapshot('test-1', updatedImage)
    };

    historyActions.record({
      operation: 'batch_background',
      description: '批量应用背景颜色: red',
      affectedImageIds: ['test-1'],
      beforeStates,
      afterStates,
    });

    expect(useUnifiedHistoryStore.getState().history).toHaveLength(1);

    historyActions.clear();

    const state = useUnifiedHistoryStore.getState();
    expect(state.history).toHaveLength(0);
    expect(state.currentIndex).toBe(-1);
    expect(state.canUndo()).toBe(false);
    expect(state.canRedo()).toBe(false);
  });
});
