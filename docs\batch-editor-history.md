# 批量编辑器历史记录功能

## 概述

批量编辑器现在支持完整的历史记录功能，包括撤销（Undo）和重做（Redo）操作。该功能基于统一历史记录系统实现，确保所有批量操作都能被正确记录和恢复。

## 功能特性

### 1. 操作记录
- ✅ **批量背景处理**：记录背景颜色/图片的批量应用和背景去除
- ✅ **批量尺寸调整**：记录固定尺寸、宽高比、比例缩放等操作
- ✅ **批量重命名**：记录前缀、起始序号、间隔等设置
- ✅ **批量格式转换**：记录目标格式转换操作
- ✅ **批量压缩**：记录压缩级别和自定义大小设置

### 2. 去重机制
- 自动检测相同操作，避免重复记录
- 只有当图片状态真正发生变化时才记录新的历史项
- 比较关键属性：名称、背景、尺寸、格式、压缩等

### 3. 用户界面
- 前进/后退按钮位于图片网格区域的左上角
- 按钮状态根据历史记录可用性自动更新
- 禁用状态下按钮透明度为0.3

## 技术实现

### 核心组件

#### 1. UnifiedHistoryStore
```typescript
// 统一历史记录存储
export const useUnifiedHistoryStore = create<HistoryState & HistoryActions>()
```

**主要功能：**
- 记录操作前后的图片状态快照
- 支持撤销/重做操作
- 自动去重相同操作
- 限制历史记录数量（默认50条）

#### 2. useUnifiedHistory Hook
```typescript
// 统一历史记录管理Hook
export const useUnifiedHistory = () => {
  // 返回历史记录操作接口
}
```

**主要方法：**
- `recordBatchOperation()`: 记录批量操作
- `handleUndo()`: 执行撤销
- `handleRedo()`: 执行重做
- `recordInitialState()`: 记录初始状态

### 数据结构

#### HistoryItem
```typescript
interface HistoryItem {
  id: string;
  timestamp: number;
  operation: OperationType;
  description: string;
  affectedImageIds: string[];
  beforeStates: Record<string, ImageSnapshot>;
  afterStates: Record<string, ImageSnapshot>;
  metadata?: Record<string, any>;
}
```

#### ImageSnapshot
```typescript
interface ImageSnapshot {
  id: string;
  name: string;
  width: number;
  height: number;
  size: number;
  status: ImageState['status'];
  // ... 其他关键属性
}
```

## 使用方式

### 在批量编辑器中集成

```typescript
export function BatchEditor() {
  // 获取历史记录功能
  const { recordBatchOperation, recordInitialState } = useUnifiedHistory();

  // 记录初始状态
  useEffect(() => {
    if (images.length > 0) {
      const imageIds = images.map(img => img.id);
      recordInitialState(imageIds);
    }
  }, [images.length, recordInitialState]);

  // 应用操作时记录历史
  const handleApplySettings = async (settings: BackgroundSettings) => {
    const imageIds = images.map(img => img.id);
    
    // 记录操作前状态
    const recordAfterOperation = recordBatchOperation(
      imageIds,
      'batch_background',
      `批量应用背景颜色: ${settings.color}`,
      { settings }
    );

    // 执行操作
    // ... 批量处理逻辑

    // 记录操作后状态
    recordAfterOperation();
  };
}
```

### 在UI组件中使用

```typescript
export function ImageUploadGrid() {
  // 获取历史记录状态和操作
  const { canUndo, canRedo, handleUndo, handleRedo } = useUnifiedHistory();

  return (
    <div>
      {/* 撤销按钮 */}
      <Button
        onClick={() => handleUndo()}
        disabled={!canUndo}
        style={{ opacity: canUndo ? 1 : 0.3 }}
      >
        Undo
      </Button>

      {/* 重做按钮 */}
      <Button
        onClick={() => handleRedo()}
        disabled={!canRedo}
        style={{ opacity: canRedo ? 1 : 0.3 }}
      >
        Redo
      </Button>
    </div>
  );
}
```

## 操作类型

支持的操作类型包括：

- `upload`: 上传图片（初始状态）
- `batch_background`: 批量背景处理（包括背景去除、应用背景颜色和背景图片）
- `batch_resize`: 批量尺寸调整
- `batch_rename`: 批量重命名
- `batch_convert`: 批量格式转换
- `batch_compress`: 批量压缩

## 注意事项

### 1. 性能考虑
- 历史记录限制为50条，避免内存过度使用
- 状态快照只保存关键属性，减少存储开销
- 去重机制避免不必要的记录

### 2. 状态管理
- 撤销/重做时会暂停历史记录，避免产生新的记录
- 恢复状态后会异步更新预览URL
- 支持批量更新多张图片的状态

### 3. 用户体验
- 按钮状态实时更新，清晰显示可用操作
- 操作描述清晰，便于用户理解历史记录
- 支持键盘快捷键（可扩展）

## 测试

项目包含完整的单元测试，覆盖：
- 基本的记录/撤销/重做功能
- 去重机制
- 历史记录限制
- 状态恢复

运行测试：
```bash
npm test src/lib/store/__tests__/unifiedHistoryStore.test.ts
```

## 扩展性

该历史记录系统设计为通用系统，可以轻松扩展到：
- 单个图片编辑器
- 其他批量操作
- 自定义操作类型
- 更复杂的状态比较逻辑
